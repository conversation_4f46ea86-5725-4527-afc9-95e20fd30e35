@echo off
echo WiFi Adapter Optimizer - Installation Script
echo ============================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please run as Administrator.
    echo.
    echo Right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo Checking system requirements...

REM Check if .NET 6 is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo .NET 6.0 is not installed.
    echo.
    echo Would you like to download .NET 6.0 now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Opening .NET 6.0 download page...
        start https://dotnet.microsoft.com/download/dotnet/6.0
        echo.
        echo Please install .NET 6.0 and run this script again.
        pause
        exit /b 1
    ) else (
        echo Installation cancelled.
        pause
        exit /b 1
    )
)

echo .NET 6.0 found - OK
echo.

REM Build the application if not already built
if not exist "dist\WiFiOptimizer.exe" (
    echo Building WiFi Optimizer...
    call .\build.bat
    if not exist "dist\WiFiOptimizer.exe" (
        echo Build failed. Installation cancelled.
        pause
        exit /b 1
    )
)

echo Installing WiFi Optimizer...

REM Create installation directory
set INSTALL_DIR=%ProgramFiles%\WiFiOptimizer
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying files to installation directory...
copy "dist\WiFiOptimizer.exe" "%INSTALL_DIR%\" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Failed to copy WiFiOptimizer.exe
    echo Make sure the file exists and you have administrator privileges.
    pause
    exit /b 1
)
copy "README.md" "%INSTALL_DIR%\" >nul 2>&1

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\Desktop
echo Creating desktop shortcut...
powershell -ExecutionPolicy Bypass -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\WiFi Optimizer.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\WiFiOptimizer.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Enhanced WiFi Adapter Optimizer - 24/7 WiFi Speed Optimization with AI'; $Shortcut.Save()"
if errorlevel 1 (
    echo WARNING: Could not create desktop shortcut. You can manually create one later.
)

REM Add to startup (registry)
echo Configuring auto-start...
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "WiFiOptimizer" /t REG_SZ /d "\"%INSTALL_DIR%\WiFiOptimizer.exe\"" /f >nul

echo.
echo ============================================
echo Installation completed successfully!
echo ============================================
echo.
echo Installation location: %INSTALL_DIR%
echo Desktop shortcut: Created
echo Auto-start: Enabled
echo.
echo The WiFi Optimizer will:
echo - Start automatically with Windows
echo - Run 24/7 in the system tray
echo - Optimize your WiFi adapter continuously
echo - Use minimal system resources (^<10MB RAM)
echo.
echo To start now: Double-click the desktop shortcut
echo To uninstall: Run uninstall.bat
echo.
echo Would you like to start WiFi Optimizer now? (Y/N)
set /p startNow=
if /i "%startNow%"=="Y" (
    echo Starting WiFi Optimizer...
    start "" "%INSTALL_DIR%\WiFiOptimizer.exe"
    echo.
    echo WiFi Optimizer is now running in the system tray.
    echo Look for the green/red circle icon in the bottom-right corner.
)

echo.
echo Installation complete!
pause
