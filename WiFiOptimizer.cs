using System;
using System.Collections.Generic;
using System.Management;
using System.Net.NetworkInformation;
using System.Text;
using Microsoft.Win32;

namespace WiFiOptimizer
{
    public class WiFiOptimizer
    {
        private List<string> optimizedAdapters = new List<string>();

        public void OptimizeAdapters()
        {
            try
            {
                OptimizeWiFiAdapters();
                OptimizePowerSettings();
                OptimizeRegistrySettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Optimization error: {ex.Message}");
            }
        }

        private void OptimizeWiFiAdapters()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionStatus=2 AND AdapterTypeId=9");

                foreach (ManagementObject adapter in searcher.Get())
                {
                    string? deviceId = adapter["DeviceID"]?.ToString();
                    string? name = adapter["Name"]?.ToString();

                    if (deviceId != null && name != null && name.ToLower().Contains("wi-fi"))
                    {
                        OptimizeAdapterSettings(deviceId, name);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"WiFi adapter optimization error: {ex.Message}");
            }
        }

        private void OptimizeAdapterSettings(string deviceId, string adapterName)
        {
            try
            {
                // Disable power management
                SetAdapterPowerManagement(deviceId, false);

                // Optimize adapter properties via registry
                OptimizeAdapterRegistry(deviceId);

                if (!optimizedAdapters.Contains(deviceId))
                {
                    optimizedAdapters.Add(deviceId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Adapter settings optimization error: {ex.Message}");
            }
        }

        private void SetAdapterPowerManagement(string deviceId, bool allowPowerOff)
        {
            try
            {
                string registryPath = $@"SYSTEM\CurrentControlSet\Control\Class\{{4d36e972-e325-11ce-bfc1-08002be10318}}\{deviceId.PadLeft(4, '0')}";
                
                using (RegistryKey? key = Registry.LocalMachine.OpenSubKey(registryPath, true))
                {
                    if (key != null)
                    {
                        // Disable power saving
                        key.SetValue("PnPCapabilities", allowPowerOff ? 0 : 24, RegistryValueKind.DWord);
                        
                        // Set to maximum performance
                        key.SetValue("PowerSaveMode", "0", RegistryValueKind.String);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Power management error: {ex.Message}");
            }
        }

        private void OptimizeAdapterRegistry(string deviceId)
        {
            try
            {
                string registryPath = $@"SYSTEM\CurrentControlSet\Control\Class\{{4d36e972-e325-11ce-bfc1-08002be10318}}\{deviceId.PadLeft(4, '0')}";
                
                using (RegistryKey? key = Registry.LocalMachine.OpenSubKey(registryPath, true))
                {
                    if (key != null)
                    {
                        // Optimize various adapter settings
                        var optimizations = new Dictionary<string, object>
                        {
                            {"TxPower", "100"},                    // Maximum transmit power
                            {"RoamingAggressiveness", "3"},        // Medium-High roaming
                            {"ChannelWidth", "4"},                 // Auto channel width
                            {"WirelessMode", "8"},                 // 802.11ac/ax mode
                            {"ThroughputBoosterEnabled", "1"},     // Enable throughput booster
                            {"FatChannelIntolerant", "0"},         // Allow fat channels
                            {"BGScanPeriod", "60"},                // Background scan period
                            {"ScanWhenAssociated", "0"},           // Reduce scanning when connected
                            {"PowerSaveMode", "0"},                // Disable power save
                            {"ReduceSpeedOnPowerLow", "0"},        // Don't reduce speed on low power
                        };

                        foreach (var setting in optimizations)
                        {
                            try
                            {
                                key.SetValue(setting.Key, setting.Value, RegistryValueKind.String);
                            }
                            catch
                            {
                                // Some settings might not exist on all adapters
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Registry optimization error: {ex.Message}");
            }
        }

        private void OptimizePowerSettings()
        {
            try
            {
                // Set WiFi adapter to never turn off
                using (RegistryKey? key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true))
                {
                    if (key != null)
                    {
                        key.SetValue("DisableTaskOffload", 0, RegistryValueKind.DWord);
                        key.SetValue("EnableWsd", 0, RegistryValueKind.DWord);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Power settings error: {ex.Message}");
            }
        }

        private void OptimizeRegistrySettings()
        {
            try
            {
                // TCP/IP optimizations for better throughput
                using (RegistryKey? key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true))
                {
                    if (key != null)
                    {
                        key.SetValue("TcpAckFrequency", 1, RegistryValueKind.DWord);
                        key.SetValue("TCPNoDelay", 1, RegistryValueKind.DWord);
                        key.SetValue("TcpDelAckTicks", 0, RegistryValueKind.DWord);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Registry settings error: {ex.Message}");
            }
        }

        public bool IsWiFiConnected()
        {
            try
            {
                NetworkInterface[] interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (NetworkInterface ni in interfaces)
                {
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211 && 
                        ni.OperationalStatus == OperationalStatus.Up)
                    {
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"WiFi connection check error: {ex.Message}");
            }
            return false;
        }

        public string GetStatusReport()
        {
            StringBuilder status = new StringBuilder();
            status.AppendLine("WiFi Adapter Optimizer Status");
            status.AppendLine("================================");
            status.AppendLine();
            
            status.AppendLine($"WiFi Connected: {(IsWiFiConnected() ? "Yes" : "No")}");
            status.AppendLine($"Optimized Adapters: {optimizedAdapters.Count}");
            status.AppendLine($"Last Check: {DateTime.Now:HH:mm:ss}");
            status.AppendLine();
            
            status.AppendLine("Active Optimizations:");
            status.AppendLine("• Power management disabled");
            status.AppendLine("• Maximum transmit power");
            status.AppendLine("• Optimized channel settings");
            status.AppendLine("• Enhanced throughput mode");
            status.AppendLine("• TCP/IP stack optimized");
            
            return status.ToString();
        }
    }
}
