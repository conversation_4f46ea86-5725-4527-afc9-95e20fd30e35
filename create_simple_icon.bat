@echo off
echo Creating WiFi Optimizer Icon...
echo ================================

REM Create a simple icon using PowerShell and .NET
powershell -ExecutionPolicy Bypass -Command ^
"Add-Type -AssemblyName System.Drawing; ^
Add-Type -AssemblyName System.Windows.Forms; ^
$bitmap = New-Object System.Drawing.Bitmap(64, 64); ^
$graphics = [System.Drawing.Graphics]::FromImage($bitmap); ^
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias; ^
$blueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 120, 215)); ^
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White); ^
$greenBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 255, 127)); ^
$whitePen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 3); ^
$graphics.FillEllipse($blueBrush, 4, 4, 56, 56); ^
$center = 32; ^
$graphics.DrawArc($whitePen, $center-20, $center-15, 40, 30, 200, 140); ^
$graphics.DrawArc($whitePen, $center-15, $center-10, 30, 20, 200, 140); ^
$graphics.DrawArc($whitePen, $center-10, $center-5, 20, 10, 200, 140); ^
$graphics.FillEllipse($whiteBrush, $center-3, $center+5, 6, 6); ^
$font = New-Object System.Drawing.Font('Arial', 8, [System.Drawing.FontStyle]::Bold); ^
$graphics.DrawString('AI', $font, $greenBrush, 45, 45); ^
$bitmap.Save('wifi-optimizer-temp.png', [System.Drawing.Imaging.ImageFormat]::Png); ^
$graphics.Dispose(); $bitmap.Dispose(); $blueBrush.Dispose(); $whiteBrush.Dispose(); $greenBrush.Dispose(); $whitePen.Dispose(); $font.Dispose(); ^
Write-Host 'Icon created successfully!'"

if exist "wifi-optimizer-temp.png" (
    echo ✅ PNG icon created successfully
    
    REM Convert PNG to ICO using online converter or manual process
    echo.
    echo To create the ICO file:
    echo 1. Go to https://convertio.co/png-ico/
    echo 2. Upload wifi-optimizer-temp.png
    echo 3. Download as wifi-optimizer.ico
    echo 4. Place the .ico file in this folder
    echo.
    echo Or rename wifi-optimizer-temp.png to wifi-optimizer.ico for basic support
    
    REM For now, copy PNG as ICO (basic support)
    copy "wifi-optimizer-temp.png" "wifi-optimizer.ico" >nul 2>&1
    echo ✅ Basic ICO file created
    
) else (
    echo ❌ Failed to create icon
    echo.
    echo Alternative: Download a WiFi icon from:
    echo • https://icons8.com/icons/set/wifi
    echo • https://www.flaticon.com/search?word=wifi
    echo Save as 'wifi-optimizer.ico' in this folder
)

echo.
echo Icon creation complete!
pause
