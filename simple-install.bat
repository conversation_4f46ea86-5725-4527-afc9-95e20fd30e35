@echo off
echo Enhanced WiFi Optimizer - Simple Installation
echo =============================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ This script requires administrator privileges.
    echo.
    echo Please:
    echo 1. Right-click this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo ✅ Administrator privileges confirmed
echo.

REM Check if application is built
if not exist "dist\WiFiOptimizer.exe" (
    echo ❌ WiFiOptimizer.exe not found in dist folder
    echo.
    echo Please build the application first:
    echo 1. Open Command Prompt as Administrator
    echo 2. Navigate to this folder
    echo 3. Run: .\build.bat
    echo 4. Then run this installer again
    echo.
    pause
    exit /b 1
)

echo ✅ WiFiOptimizer.exe found
echo.

REM Create installation directory
set INSTALL_DIR=%ProgramFiles%\WiFiOptimizer
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    if errorlevel 1 (
        echo ❌ Failed to create installation directory
        echo Make sure you're running as administrator
        pause
        exit /b 1
    )
)
echo ✅ Installation directory ready
echo.

REM Copy main executable
echo Copying WiFiOptimizer.exe...
copy "dist\WiFiOptimizer.exe" "%INSTALL_DIR%\" >nul 2>&1
if errorlevel 1 (
    echo ❌ Failed to copy WiFiOptimizer.exe
    echo Check if the file is in use or if you have permissions
    pause
    exit /b 1
)
echo ✅ WiFiOptimizer.exe copied successfully
echo.

REM Copy documentation
if exist "README.md" (
    copy "README.md" "%INSTALL_DIR%\" >nul 2>&1
    echo ✅ README.md copied
) else (
    echo ⚠️  README.md not found (optional)
)
echo.

REM Create desktop shortcut (simplified method)
set DESKTOP=%USERPROFILE%\Desktop
echo Creating desktop shortcut...

REM Try PowerShell method first
powershell -ExecutionPolicy Bypass -Command "try { $WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\WiFi Optimizer.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\WiFiOptimizer.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Enhanced WiFi Optimizer - AI-Powered Network Optimization'; $Shortcut.Save(); Write-Host 'Shortcut created successfully' } catch { Write-Host 'Shortcut creation failed' }" >nul 2>&1

if exist "%DESKTOP%\WiFi Optimizer.lnk" (
    echo ✅ Desktop shortcut created
) else (
    echo ⚠️  Desktop shortcut creation failed (you can create manually)
)
echo.

REM Add to Windows startup
echo Configuring auto-start with Windows...
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "WiFiOptimizer" /t REG_SZ /d "\"%INSTALL_DIR%\WiFiOptimizer.exe\"" /f >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Auto-start configuration failed (you can add manually)
) else (
    echo ✅ Auto-start configured successfully
)
echo.

echo =============================================
echo 🎉 INSTALLATION COMPLETED SUCCESSFULLY! 🎉
echo =============================================
echo.
echo 📍 Installation Location: %INSTALL_DIR%
echo 🖥️  Desktop Shortcut: WiFi Optimizer.lnk
echo 🚀 Auto-Start: Enabled with Windows
echo.
echo 🔥 ENHANCED FEATURES INCLUDED:
echo   • 🤖 AI-Powered Network Optimization
echo   • 🎮 Gaming Mode with Ultra-Low Latency
echo   • ⚡ Real-Time Speed Monitoring
echo   • 📊 Live Performance Dashboard
echo   • 🛡️ Advanced WiFi Adapter Optimization
echo   • 📱 24/7 Background Monitoring
echo.
echo 🎯 TO START THE OPTIMIZER:
echo   1. Double-click the desktop shortcut, OR
echo   2. Run from Start Menu, OR
echo   3. Start now from this installer
echo.
echo The application will run in the system tray (bottom-right corner)
echo Look for a green/red circle icon
echo.
set /p startNow=Would you like to start WiFi Optimizer now? (Y/N): 
if /i "%startNow%"=="Y" (
    echo.
    echo 🚀 Starting Enhanced WiFi Optimizer...
    start "" "%INSTALL_DIR%\WiFiOptimizer.exe"
    timeout /t 3 >nul
    echo.
    echo ✅ WiFi Optimizer is now running!
    echo 👀 Look for the icon in your system tray (bottom-right)
    echo 🖱️  Right-click the icon to access all features
    echo.
    echo 🎮 Try the Gaming Mode for ultra-low latency!
    echo 📊 Open the Dashboard to see real-time performance!
)

echo.
echo 🎉 Installation Complete! Enjoy your optimized WiFi! 🎉
echo.
pause
