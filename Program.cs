using System;
using System.Windows.Forms;
using System.Threading;

namespace WiFiOptimizer
{
    internal static class Program
    {
        private static Mutex? mutex = null;

        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Check for dashboard argument
            bool showDashboard = args.Length > 0 && args[0].ToLower() == "--dashboard";

            if (showDashboard)
            {
                // Show dashboard directly without checking for existing instance
                ShowDashboardOnly();
                return;
            }

            // Ensure only one instance runs for main application
            const string appName = "WiFiAdapterOptimizer";
            bool createdNew;

            mutex = new Mutex(true, appName, out createdNew);

            if (!createdNew)
            {
                // If app is already running and user clicks from Start menu, show dashboard
                ShowDashboardFromExistingInstance();
                return;
            }

            // Run the main form
            Application.Run(new MainForm());
        }

        private static void ShowDashboardOnly()
        {
            try
            {
                // Create temporary instances for dashboard
                var speedMonitor = new SpeedMonitor();
                var gameModeOptimizer = new GameModeOptimizer();
                var aiOptimizer = new AIOptimizer();

                // Show dashboard
                var dashboard = new DashboardForm(speedMonitor, gameModeOptimizer, aiOptimizer);
                Application.Run(dashboard);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening dashboard: {ex.Message}", "Dashboard Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void ShowDashboardFromExistingInstance()
        {
            // Try to signal the existing instance to show dashboard
            try
            {
                // Send a message to show dashboard (simplified approach)
                MessageBox.Show("WiFi Optimizer is already running!\n\nTo access the dashboard:\n" +
                              "1. Look for the WiFi icon in your system tray\n" +
                              "2. Right-click the icon\n" +
                              "3. Select '📊 Dashboard'",
                              "Dashboard Access", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch
            {
                // Fallback: show dashboard anyway
                ShowDashboardOnly();
            }
        }
    }
}
