using System;
using System.Windows.Forms;
using System.Threading;

namespace WiFiOptimizer
{
    internal static class Program
    {
        private static Mutex? mutex = null;

        [STAThread]
        static void Main()
        {
            // Ensure only one instance runs
            const string appName = "WiFiAdapterOptimizer";
            bool createdNew;

            mutex = new Mutex(true, appName, out createdNew);

            if (!createdNew)
            {
                MessageBox.Show("WiFi Optimizer is already running!", "Already Running", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // Run the main form
            Application.Run(new MainForm());
        }
    }
}
