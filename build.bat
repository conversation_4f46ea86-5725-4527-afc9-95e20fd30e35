@echo off
echo Building WiFi Adapter Optimizer...
echo.

REM Check if .NET 6 is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET 6.0 SDK is required but not found.
    echo Please download and install .NET 6.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

REM Clean previous builds
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo Restoring packages...
dotnet restore

echo Building release version...
dotnet build --configuration Release --no-restore

echo Publishing self-contained executable...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "dist" -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true

echo.
if exist "dist\WiFiOptimizer.exe" (
    echo SUCCESS: WiFiOptimizer.exe created in 'dist' folder
    echo File size: 
    dir "dist\WiFiOptimizer.exe" | find "WiFiOptimizer.exe"
    echo.
    echo You can now run: dist\WiFiOptimizer.exe
    echo The application will start in system tray and run 24/7
) else (
    echo ERROR: Build failed. Check the output above for errors.
)

echo.
pause
