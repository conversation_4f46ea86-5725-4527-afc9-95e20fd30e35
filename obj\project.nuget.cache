{"version": 2, "dgSpecHash": "lQ4S9HbZ5FmM9oKJqJGLWZUmBF1Ku8GXB7vAZCIQ2h8011HSBlzD6AE6pRzmeRO5U3xnU7uAOSmMXNtEWg6OcA==", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\New folder\\WiFiOptimizer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\7.0.0\\system.diagnostics.eventlog.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.2\\system.management.7.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.serviceprocess.servicecontroller\\7.0.1\\system.serviceprocess.servicecontroller.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.runtime.win-x64\\6.0.36\\microsoft.netcore.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.runtime.win-x64\\6.0.36\\microsoft.windowsdesktop.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.runtime.win-x64\\6.0.36\\microsoft.aspnetcore.app.runtime.win-x64.6.0.36.nupkg.sha512"], "logs": []}