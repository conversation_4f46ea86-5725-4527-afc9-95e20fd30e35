#!/usr/bin/env python3
"""
WiFi Optimizer Icon Generator
Creates a professional WiFi icon for the application
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_wifi_icon():
    """Create a professional WiFi optimizer icon"""
    
    # Create a 256x256 image with transparent background
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    bg_color = (0, 120, 215, 255)  # Windows blue
    wifi_color = (255, 255, 255, 255)  # White
    accent_color = (0, 255, 127, 255)  # Green accent
    
    # Draw background circle
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color)
    
    # Draw WiFi symbol (concentric arcs)
    center_x, center_y = size // 2, size // 2 + 20
    
    # WiFi arcs (from outer to inner)
    arc_configs = [
        (80, 12, wifi_color),    # Outer arc
        (60, 10, wifi_color),    # Middle arc  
        (40, 8, wifi_color),     # Inner arc
    ]
    
    for radius, width, color in arc_configs:
        # Draw arc (top half of circle)
        bbox = [center_x - radius, center_y - radius, center_x + radius, center_y + radius]
        draw.arc(bbox, start=200, end=340, fill=color, width=width)
    
    # Draw center dot
    dot_radius = 8
    draw.ellipse([center_x - dot_radius, center_y - dot_radius, 
                  center_x + dot_radius, center_y + dot_radius], fill=wifi_color)
    
    # Draw speed/optimization indicator (lightning bolt)
    lightning_points = [
        (center_x + 30, center_y - 40),
        (center_x + 45, center_y - 20),
        (center_x + 35, center_y - 10),
        (center_x + 50, center_y + 10),
        (center_x + 25, center_y + 5),
        (center_x + 35, center_y - 15),
    ]
    draw.polygon(lightning_points, fill=accent_color)
    
    # Add "AI" text indicator
    try:
        # Try to use a system font
        font_size = 24
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    ai_text = "AI"
    # Get text bounding box
    bbox = draw.textbbox((0, 0), ai_text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Position AI text in bottom right of circle
    text_x = center_x + 35
    text_y = center_y + 25
    
    # Draw AI text background
    padding = 4
    draw.ellipse([text_x - padding, text_y - padding, 
                  text_x + text_width + padding, text_y + text_height + padding], 
                 fill=accent_color)
    
    # Draw AI text
    draw.text((text_x, text_y), ai_text, fill=(0, 0, 0, 255), font=font)
    
    return img

def create_multiple_sizes():
    """Create icon in multiple sizes for Windows"""
    base_icon = create_wifi_icon()
    
    sizes = [16, 24, 32, 48, 64, 128, 256]
    icons = []
    
    for size in sizes:
        resized = base_icon.resize((size, size), Image.Resampling.LANCZOS)
        icons.append(resized)
    
    return icons

def save_icons():
    """Save icons in various formats"""
    print("Creating WiFi Optimizer icon...")
    
    # Create icons
    icons = create_multiple_sizes()
    
    # Save as ICO file (Windows icon)
    icons[0].save('wifi-optimizer.ico', format='ICO', sizes=[(icon.size[0], icon.size[1]) for icon in icons])
    print("✅ Created wifi-optimizer.ico")
    
    # Save as PNG for other uses
    icons[-1].save('wifi-optimizer.png', format='PNG')
    print("✅ Created wifi-optimizer.png")
    
    # Save smaller PNG for system tray
    icons[4].save('wifi-optimizer-64.png', format='PNG')  # 64x64
    print("✅ Created wifi-optimizer-64.png")
    
    print("\n🎨 Icon creation complete!")
    print("Files created:")
    print("  • wifi-optimizer.ico (Windows icon)")
    print("  • wifi-optimizer.png (256x256 PNG)")
    print("  • wifi-optimizer-64.png (64x64 PNG)")

if __name__ == "__main__":
    try:
        save_icons()
    except ImportError:
        print("❌ PIL (Pillow) not installed.")
        print("Install with: pip install Pillow")
        print("\nAlternatively, I'll create a simple icon using basic methods...")
        
        # Create a simple text-based icon as fallback
        print("Creating simple fallback icon...")
        
        # This would create a basic icon - but for now, let's use the batch method below

if not os.path.exists('wifi-optimizer.ico'):
    print("\n📝 Creating simple icon using Windows tools...")
    print("You can also download a WiFi icon from:")
    print("  • https://icons8.com/icons/set/wifi")
    print("  • https://www.flaticon.com/search?word=wifi")
    print("  • Save as 'wifi-optimizer.ico' in this folder")
