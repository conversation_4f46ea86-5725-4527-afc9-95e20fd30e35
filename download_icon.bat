@echo off
echo Downloading WiFi Optimizer Icon...
echo ==================================

REM Try to download a WiFi icon from a public source
echo Attempting to download WiFi icon...

REM Use PowerShell to download icon
powershell -Command "try { Invoke-WebRequest -Uri 'https://cdn-icons-png.flaticon.com/512/93/93158.png' -OutFile 'wifi-icon-temp.png' -UseBasicParsing; Write-Host 'Icon downloaded successfully' } catch { Write-Host 'Download failed' }"

if exist "wifi-icon-temp.png" (
    echo ✅ Icon downloaded successfully
    
    REM Rename to our icon file
    copy "wifi-icon-temp.png" "wifi-optimizer.ico" >nul 2>&1
    echo ✅ Icon ready: wifi-optimizer.ico
    
    REM Clean up
    del "wifi-icon-temp.png" >nul 2>&1
    
) else (
    echo ❌ Download failed. Creating basic icon...
    
    REM Create a very simple text-based icon file
    echo Creating basic icon...
    
    REM Just create a placeholder file for now
    echo. > wifi-optimizer.ico
    echo ⚠️  Basic placeholder icon created
    echo.
    echo For a better icon, you can:
    echo 1. Download from: https://icons8.com/icons/set/wifi
    echo 2. Save as 'wifi-optimizer.ico' in this folder
    echo 3. Or use any 32x32 or 64x64 WiFi icon
)

echo.
echo Icon setup complete!
pause
