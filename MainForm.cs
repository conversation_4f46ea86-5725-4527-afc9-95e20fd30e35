using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Win32;

namespace WiFiOptimizer
{
    public partial class MainForm : Form
    {
        private NotifyIcon trayIcon = null!;
        private ContextMenuStrip trayMenu = null!;
        private WiFiOptimizer optimizer = null!;
        private SpeedMonitor speedMonitor = null!;
        private GameModeOptimizer gameModeOptimizer = null!;
        private AIOptimizer aiOptimizer = null!;
        private System.Windows.Forms.Timer optimizationTimer = null!;
        private bool isOptimizationEnabled = true;
        private SpeedTestResult? lastSpeedTest;
        private NetworkUsageData? lastNetworkUsage;

        public MainForm()
        {
            InitializeComponent();
            InitializeTrayIcon();
            InitializeOptimizer();
            SetupAutoStart();
            
            // Hide the form immediately
            this.WindowState = FormWindowState.Minimized;
            this.ShowInTaskbar = false;
            this.Visible = false;
        }

        private void InitializeComponent()
        {
            this.Text = "WiFi Adapter Optimizer";
            this.Size = new Size(1, 1);
            this.ShowInTaskbar = false;
            this.WindowState = FormWindowState.Minimized;
        }

        private void InitializeTrayIcon()
        {
            trayMenu = new ContextMenuStrip();
            trayMenu.Items.Add("🔧 Enable Optimization", null, OnToggleOptimization);
            trayMenu.Items.Add("-");
            trayMenu.Items.Add("🎮 Gaming Mode", null, OnToggleGameMode);
            trayMenu.Items.Add("⚡ Speed Test", null, OnRunSpeedTest);
            trayMenu.Items.Add("🤖 AI Status", null, OnShowAIStatus);
            trayMenu.Items.Add("-");
            trayMenu.Items.Add("📊 Dashboard", null, OnShowDashboard);
            trayMenu.Items.Add("📈 Status", null, OnShowStatus);
            trayMenu.Items.Add("⚙️ Settings", null, OnShowSettings);
            trayMenu.Items.Add("-");
            trayMenu.Items.Add("❌ Exit", null, OnExit);

            trayIcon = new NotifyIcon()
            {
                Text = "Enhanced WiFi Optimizer - AI-Powered Optimization Active",
                Icon = CreateIcon(Color.Green),
                ContextMenuStrip = trayMenu,
                Visible = true
            };

            trayIcon.DoubleClick += OnShowDashboard;
        }

        private void InitializeOptimizer()
        {
            optimizer = new WiFiOptimizer();
            speedMonitor = new SpeedMonitor();
            gameModeOptimizer = new GameModeOptimizer();
            aiOptimizer = new AIOptimizer();

            // Setup event handlers
            speedMonitor.SpeedTestCompleted += OnSpeedTestCompleted;
            speedMonitor.NetworkUsageUpdated += OnNetworkUsageUpdated;

            // Timer for periodic optimization checks
            optimizationTimer = new System.Windows.Forms.Timer();
            optimizationTimer.Interval = 30000; // 30 seconds
            optimizationTimer.Tick += OnOptimizationTimer;
            optimizationTimer.Start();

            // Start monitoring
            speedMonitor.StartMonitoring();

            // Initial optimization
            if (isOptimizationEnabled)
            {
                optimizer.OptimizeAdapters();
            }
        }

        private void SetupAutoStart()
        {
            try
            {
                using (RegistryKey? rk = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true))
                {
                    if (rk != null)
                    {
                        string appPath = Application.ExecutablePath;
                        rk.SetValue("WiFiOptimizer", appPath);
                    }
                }
            }
            catch (Exception ex)
            {
                // Silently fail if can't set auto-start
                System.Diagnostics.Debug.WriteLine($"Could not set auto-start: {ex.Message}");
            }
        }

        private Icon CreateIcon(Color color)
        {
            try
            {
                // Try to load custom icon first
                if (File.Exists("wifi-optimizer.ico"))
                {
                    return new Icon("wifi-optimizer.ico", 16, 16);
                }
            }
            catch
            {
                // Fall back to generated icon
            }

            // Create a more detailed WiFi icon
            Bitmap bitmap = new Bitmap(16, 16);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // Draw WiFi symbol
                using (Pen pen = new Pen(color, 1.5f))
                {
                    // WiFi arcs
                    g.DrawArc(pen, 2, 6, 12, 8, 200, 140);
                    g.DrawArc(pen, 4, 8, 8, 6, 200, 140);
                    g.DrawArc(pen, 6, 10, 4, 4, 200, 140);
                }

                // Center dot
                using (Brush brush = new SolidBrush(color))
                {
                    g.FillEllipse(brush, 7, 12, 2, 2);
                }

                // AI indicator (small green dot)
                if (color == Color.Green)
                {
                    using (Brush aiBrush = new SolidBrush(Color.Lime))
                    {
                        g.FillEllipse(aiBrush, 12, 2, 3, 3);
                    }
                }
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private async void OnOptimizationTimer(object? sender, EventArgs e)
        {
            if (isOptimizationEnabled)
            {
                optimizer.OptimizeAdapters();

                // AI-powered optimization
                if (lastNetworkUsage != null)
                {
                    var speedHistory = speedMonitor.GetSpeedHistory();
                    var aiRecommendation = await aiOptimizer.AnalyzeAndOptimize(lastNetworkUsage, speedHistory);

                    // Apply AI recommendations automatically
                    ApplyAIRecommendations(aiRecommendation);
                }

                UpdateTrayIcon();
            }
        }

        private void UpdateTrayIcon()
        {
            bool isConnected = optimizer.IsWiFiConnected();
            bool isGameMode = gameModeOptimizer.IsGameModeActive;

            Color iconColor = Color.Red; // Default: disconnected
            if (isConnected && isOptimizationEnabled)
            {
                iconColor = isGameMode ? Color.Orange : Color.Green;
            }

            trayIcon.Icon = CreateIcon(iconColor);

            string status = isOptimizationEnabled ? "Active" : "Disabled";
            string connection = isConnected ? "Connected" : "Disconnected";
            string mode = isGameMode ? " | Gaming Mode" : "";
            string aiInfo = lastSpeedTest != null ? $" | {lastSpeedTest.DownloadSpeed:F0}Mbps" : "";

            trayIcon.Text = $"Enhanced WiFi Optimizer - {status} - {connection}{mode}{aiInfo}";
        }

        private void OnToggleOptimization(object? sender, EventArgs e)
        {
            isOptimizationEnabled = !isOptimizationEnabled;

            var menuItem = (ToolStripMenuItem)trayMenu.Items[0];
            menuItem.Text = isOptimizationEnabled ? "🔧 Disable Optimization" : "🔧 Enable Optimization";

            if (isOptimizationEnabled)
            {
                optimizer.OptimizeAdapters();
                optimizationTimer.Start();
                speedMonitor.StartMonitoring();
            }
            else
            {
                optimizationTimer.Stop();
                speedMonitor.StopMonitoring();
            }

            UpdateTrayIcon();
        }

        private void OnToggleGameMode(object? sender, EventArgs e)
        {
            if (gameModeOptimizer.IsGameModeActive)
            {
                gameModeOptimizer.DisableGameMode();
                ((ToolStripMenuItem)trayMenu.Items[2]).Text = "🎮 Gaming Mode";
                ShowNotification("🎮 Gaming Mode Disabled", "Normal performance mode restored");
            }
            else
            {
                gameModeOptimizer.EnableGameMode();
                ((ToolStripMenuItem)trayMenu.Items[2]).Text = "🎮 Disable Gaming Mode";
                ShowNotification("🎮 Gaming Mode Activated!", "Ultra-low latency enabled for gaming");
            }
            UpdateTrayIcon();
        }

        private async void OnRunSpeedTest(object? sender, EventArgs e)
        {
            ShowNotification("⚡ Speed Test Starting", "Testing your connection speed...");

            try
            {
                var result = await speedMonitor.PerformSpeedTest();
                lastSpeedTest = result;

                ShowNotification("⚡ Speed Test Complete", result.GetSummary());
            }
            catch (Exception ex)
            {
                ShowNotification("❌ Speed Test Failed", $"Error: {ex.Message}");
            }
        }

        private void OnShowAIStatus(object? sender, EventArgs e)
        {
            string aiStatus = aiOptimizer.GetAIStatus();
            MessageBox.Show(aiStatus, "🤖 AI Optimizer Status", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OnShowDashboard(object? sender, EventArgs e)
        {
            var dashboard = new DashboardForm(speedMonitor, gameModeOptimizer, aiOptimizer);
            dashboard.Show();
        }

        private void OnShowStatus(object? sender, EventArgs e)
        {
            var status = GetComprehensiveStatus();
            MessageBox.Show(status, "📈 WiFi Optimizer Status", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OnShowSettings(object? sender, EventArgs e)
        {
            string features = "⚙️ ENHANCED WIFI OPTIMIZER FEATURES:\n\n" +
                            "🔧 Auto WiFi adapter optimization\n" +
                            "🎮 Gaming mode with ultra-low latency\n" +
                            "⚡ Real-time speed monitoring\n" +
                            "🤖 AI-powered network optimization\n" +
                            "📊 Live performance analytics\n" +
                            "🛡️ Advanced network security\n" +
                            "📱 24/7 background monitoring\n" +
                            "💾 Minimal RAM usage (<10MB)\n\n" +
                            "🚀 All features are automatically active!";

            MessageBox.Show(features, "⚙️ Enhanced Settings", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OnExit(object? sender, EventArgs e)
        {
            trayIcon.Visible = false;
            optimizationTimer?.Stop();
            speedMonitor?.StopMonitoring();
            speedMonitor?.Dispose();
            Application.Exit();
        }

        private void OnSpeedTestCompleted(object? sender, SpeedTestResult result)
        {
            lastSpeedTest = result;
            UpdateTrayIcon();

            // Show notification for significant speed changes
            var previousTest = speedMonitor.GetSpeedHistory().LastOrDefault();
            if (previousTest != null)
            {
                var downloadChange = ((result.DownloadSpeed - previousTest.DownloadSpeed) / previousTest.DownloadSpeed) * 100;
                if (Math.Abs(downloadChange) > 20) // 20% change
                {
                    string direction = downloadChange > 0 ? "📈 Improved" : "📉 Decreased";
                    ShowNotification($"Speed {direction}", $"Download: {result.DownloadSpeed:F1} Mbps ({downloadChange:+0;-0}%)");
                }
            }
        }

        private void OnNetworkUsageUpdated(object? sender, NetworkUsageData usage)
        {
            lastNetworkUsage = usage;
        }

        private void ApplyAIRecommendations(OptimizationRecommendation recommendation)
        {
            if (recommendation.Confidence < 0.5) return; // Only apply high-confidence recommendations

            foreach (var action in recommendation.Actions)
            {
                if (action.Contains("gaming mode") && !gameModeOptimizer.IsGameModeActive)
                {
                    gameModeOptimizer.EnableGameMode();
                }
                // Add more automatic optimizations based on AI recommendations
            }
        }

        private string GetComprehensiveStatus()
        {
            var status = new System.Text.StringBuilder();
            status.AppendLine("📈 ENHANCED WIFI OPTIMIZER STATUS");
            status.AppendLine("=====================================");
            status.AppendLine();

            // Basic status
            status.AppendLine($"🔧 Optimization: {(isOptimizationEnabled ? "✅ Active" : "❌ Disabled")}");
            status.AppendLine($"📡 WiFi Connected: {(optimizer.IsWiFiConnected() ? "✅ Yes" : "❌ No")}");
            status.AppendLine($"🎮 Gaming Mode: {(gameModeOptimizer.IsGameModeActive ? "✅ Active" : "❌ Inactive")}");
            status.AppendLine();

            // Speed test results
            if (lastSpeedTest != null)
            {
                status.AppendLine("⚡ LATEST SPEED TEST:");
                status.AppendLine($"   ⬇️ Download: {lastSpeedTest.DownloadSpeed:F1} Mbps");
                status.AppendLine($"   ⬆️ Upload: {lastSpeedTest.UploadSpeed:F1} Mbps");
                status.AppendLine($"   📶 Ping: {lastSpeedTest.Ping:F0} ms");
                status.AppendLine($"   🕒 Tested: {lastSpeedTest.Timestamp:HH:mm:ss}");
                status.AppendLine();
            }

            // Real-time usage
            if (lastNetworkUsage != null)
            {
                status.AppendLine("📊 CURRENT USAGE:");
                status.AppendLine($"   ⬇️ Download: {lastNetworkUsage.DownloadMbps:F1} Mbps");
                status.AppendLine($"   ⬆️ Upload: {lastNetworkUsage.UploadMbps:F1} Mbps");
                status.AppendLine();
            }

            // AI status
            status.AppendLine("🤖 AI OPTIMIZER:");
            var aiStatus = aiOptimizer.GetAIStatus().Replace("🤖 AI Learning Status:\n", "");
            status.AppendLine($"   {aiStatus.Replace("\n", "\n   ")}");
            status.AppendLine();

            status.AppendLine("🚀 ACTIVE OPTIMIZATIONS:");
            status.AppendLine("   • Power management disabled");
            status.AppendLine("   • Maximum transmit power");
            status.AppendLine("   • AI-powered optimization");
            status.AppendLine("   • Real-time monitoring");
            status.AppendLine("   • Gaming mode available");

            return status.ToString();
        }

        private void ShowNotification(string title, string message)
        {
            trayIcon.ShowBalloonTip(3000, title, message, ToolTipIcon.Info);
        }

        protected override void SetVisibleCore(bool value)
        {
            base.SetVisibleCore(false);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                trayIcon?.Dispose();
                trayMenu?.Dispose();
                optimizationTimer?.Dispose();
                speedMonitor?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
