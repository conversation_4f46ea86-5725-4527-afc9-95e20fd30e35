using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Win32;

namespace WiFiOptimizer
{
    public partial class MainForm : Form
    {
        private NotifyIcon trayIcon = null!;
        private ContextMenuStrip trayMenu = null!;
        private WiFiOptimizer optimizer = null!;
        private System.Windows.Forms.Timer optimizationTimer = null!;
        private bool isOptimizationEnabled = true;

        public MainForm()
        {
            InitializeComponent();
            InitializeTrayIcon();
            InitializeOptimizer();
            SetupAutoStart();
            
            // Hide the form immediately
            this.WindowState = FormWindowState.Minimized;
            this.ShowInTaskbar = false;
            this.Visible = false;
        }

        private void InitializeComponent()
        {
            this.Text = "WiFi Adapter Optimizer";
            this.Size = new Size(1, 1);
            this.ShowInTaskbar = false;
            this.WindowState = FormWindowState.Minimized;
        }

        private void InitializeTrayIcon()
        {
            trayMenu = new ContextMenuStrip();
            trayMenu.Items.Add("Enable Optimization", null, OnToggleOptimization);
            trayMenu.Items.Add("-");
            trayMenu.Items.Add("Status", null, OnShowStatus);
            trayMenu.Items.Add("Settings", null, OnShowSettings);
            trayMenu.Items.Add("-");
            trayMenu.Items.Add("Exit", null, OnExit);

            trayIcon = new NotifyIcon()
            {
                Text = "WiFi Adapter Optimizer - Active",
                Icon = CreateIcon(Color.Green),
                ContextMenuStrip = trayMenu,
                Visible = true
            };

            trayIcon.DoubleClick += OnShowStatus;
        }

        private void InitializeOptimizer()
        {
            optimizer = new WiFiOptimizer();
            
            // Timer for periodic optimization checks
            optimizationTimer = new System.Windows.Forms.Timer();
            optimizationTimer.Interval = 30000; // 30 seconds
            optimizationTimer.Tick += OnOptimizationTimer;
            optimizationTimer.Start();

            // Initial optimization
            if (isOptimizationEnabled)
            {
                optimizer.OptimizeAdapters();
            }
        }

        private void SetupAutoStart()
        {
            try
            {
                using (RegistryKey? rk = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true))
                {
                    if (rk != null)
                    {
                        string appPath = Application.ExecutablePath;
                        rk.SetValue("WiFiOptimizer", appPath);
                    }
                }
            }
            catch (Exception ex)
            {
                // Silently fail if can't set auto-start
                System.Diagnostics.Debug.WriteLine($"Could not set auto-start: {ex.Message}");
            }
        }

        private Icon CreateIcon(Color color)
        {
            Bitmap bitmap = new Bitmap(16, 16);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.Transparent);
                using (Brush brush = new SolidBrush(color))
                {
                    g.FillEllipse(brush, 2, 2, 12, 12);
                }
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private void OnOptimizationTimer(object? sender, EventArgs e)
        {
            if (isOptimizationEnabled)
            {
                optimizer.OptimizeAdapters();
                UpdateTrayIcon();
            }
        }

        private void UpdateTrayIcon()
        {
            bool isConnected = optimizer.IsWiFiConnected();
            trayIcon.Icon = CreateIcon(isConnected && isOptimizationEnabled ? Color.Green : Color.Red);
            trayIcon.Text = $"WiFi Optimizer - {(isOptimizationEnabled ? "Active" : "Disabled")} - {(isConnected ? "Connected" : "Disconnected")}";
        }

        private void OnToggleOptimization(object? sender, EventArgs e)
        {
            isOptimizationEnabled = !isOptimizationEnabled;

            var menuItem = (ToolStripMenuItem)trayMenu.Items[0];
            menuItem.Text = isOptimizationEnabled ? "Disable Optimization" : "Enable Optimization";

            if (isOptimizationEnabled)
            {
                optimizer.OptimizeAdapters();
                optimizationTimer.Start();
            }
            else
            {
                optimizationTimer.Stop();
            }

            UpdateTrayIcon();
        }

        private void OnShowStatus(object? sender, EventArgs e)
        {
            string status = optimizer.GetStatusReport();
            MessageBox.Show(status, "WiFi Optimizer Status", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OnShowSettings(object? sender, EventArgs e)
        {
            MessageBox.Show("Settings panel coming soon!\n\nCurrent features:\n- Auto WiFi adapter optimization\n- 24/7 background monitoring\n- Minimal RAM usage", 
                "Settings", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OnExit(object? sender, EventArgs e)
        {
            trayIcon.Visible = false;
            optimizationTimer?.Stop();
            Application.Exit();
        }

        protected override void SetVisibleCore(bool value)
        {
            base.SetVisibleCore(false);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                trayIcon?.Dispose();
                trayMenu?.Dispose();
                optimizationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
