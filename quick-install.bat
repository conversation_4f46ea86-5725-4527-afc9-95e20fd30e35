@echo off
echo Enhanced WiFi Optimizer - Quick Installation
echo ============================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script requires administrator privileges.
    echo.
    echo Please:
    echo 1. Right-click this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo ✅ Administrator privileges confirmed
echo.

REM Skip .NET check and proceed directly
echo Skipping .NET check (assuming it's installed since build worked)
echo.

REM Check if application is built
if not exist "dist\WiFiOptimizer.exe" (
    echo ERROR: WiFiOptimizer.exe not found in dist folder
    echo.
    echo The application needs to be built first.
    echo Since .\build.bat worked before, the file should exist.
    echo.
    echo Current directory: %CD%
    dir dist\*.exe 2>nul
    echo.
    pause
    exit /b 1
)

echo ✅ WiFiOptimizer.exe found
echo File size:
dir "dist\WiFiOptimizer.exe" | find "WiFiOptimizer.exe"
echo.

REM Create installation directory
set INSTALL_DIR=%ProgramFiles%\WiFiOptimizer
echo Creating installation directory: %INSTALL_DIR%

if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    if errorlevel 1 (
        echo ERROR: Failed to create installation directory
        pause
        exit /b 1
    )
)

echo ✅ Installation directory ready
echo.

REM Copy files
echo Copying WiFiOptimizer.exe...
copy "dist\WiFiOptimizer.exe" "%INSTALL_DIR%\" 
if errorlevel 1 (
    echo ERROR: Failed to copy WiFiOptimizer.exe
    pause
    exit /b 1
)

echo ✅ WiFiOptimizer.exe copied successfully
echo.

REM Copy README if it exists
if exist "README.md" (
    copy "README.md" "%INSTALL_DIR%\" >nul
    echo ✅ README.md copied
)

REM Create desktop shortcut (simple method)
set DESKTOP=%USERPROFILE%\Desktop
echo Creating desktop shortcut...

REM Create a simple batch file shortcut instead of using PowerShell
echo @echo off > "%DESKTOP%\Start WiFi Optimizer.bat"
echo start "" "%INSTALL_DIR%\WiFiOptimizer.exe" >> "%DESKTOP%\Start WiFi Optimizer.bat"

if exist "%DESKTOP%\Start WiFi Optimizer.bat" (
    echo ✅ Desktop shortcut created: "Start WiFi Optimizer.bat"
) else (
    echo WARNING: Could not create desktop shortcut
)
echo.

REM Add to startup
echo Configuring auto-start...
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "WiFiOptimizer" /t REG_SZ /d "\"%INSTALL_DIR%\WiFiOptimizer.exe\"" /f
if errorlevel 1 (
    echo WARNING: Could not configure auto-start
) else (
    echo ✅ Auto-start configured
)
echo.

echo ============================================
echo 🎉 INSTALLATION COMPLETED! 🎉
echo ============================================
echo.
echo 📍 Installed to: %INSTALL_DIR%
echo 🖥️  Desktop shortcut: "Start WiFi Optimizer.bat"
echo 🚀 Auto-start: Configured
echo.
echo 🔥 ENHANCED FEATURES:
echo   • 🤖 AI-Powered Optimization
echo   • 🎮 Gaming Mode (Ultra-Low Latency)
echo   • ⚡ Real-Time Speed Monitoring
echo   • 📊 Performance Dashboard
echo   • 🛡️ Advanced WiFi Optimization
echo.
echo 🎯 TO START:
echo   1. Double-click "Start WiFi Optimizer.bat" on desktop
echo   2. Or run: "%INSTALL_DIR%\WiFiOptimizer.exe"
echo.
echo The app will appear as a green/red icon in system tray
echo Right-click the tray icon for all features!
echo.

set /p startNow=Start WiFi Optimizer now? (Y/N): 
if /i "%startNow%"=="Y" (
    echo.
    echo 🚀 Starting Enhanced WiFi Optimizer...
    start "" "%INSTALL_DIR%\WiFiOptimizer.exe"
    echo.
    echo ✅ WiFi Optimizer started!
    echo 👀 Look for the icon in system tray (bottom-right)
    echo 🖱️  Right-click for Gaming Mode, Speed Test, Dashboard
)

echo.
echo 🎉 Installation Complete! 🎉
pause
