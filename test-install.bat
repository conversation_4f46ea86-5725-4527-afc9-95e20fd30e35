@echo off
echo WiFi Optimizer Installation Diagnostics
echo ========================================
echo.

echo 1. Checking current directory...
echo Current directory: %CD%
echo.

echo 2. Checking if WiFiOptimizer.exe exists...
if exist "dist\WiFiOptimizer.exe" (
    echo ✅ dist\WiFiOptimizer.exe found
    dir "dist\WiFiOptimizer.exe"
) else (
    echo ❌ dist\WiFiOptimizer.exe NOT found
    echo You need to build the application first by running: .\build.bat
)
echo.

echo 3. Checking .NET installation...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET 6.0 not found
    echo Please install .NET 6.0 from: https://dotnet.microsoft.com/download/dotnet/6.0
) else (
    echo ✅ .NET found
    dotnet --version
)
echo.

echo 4. Checking administrator privileges...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Not running as administrator
    echo Right-click this file and select "Run as administrator"
) else (
    echo ✅ Running as administrator
)
echo.

echo 5. Checking Program Files access...
set TEST_DIR=%ProgramFiles%\WiFiOptimizerTest
mkdir "%TEST_DIR%" >nul 2>&1
if errorlevel 1 (
    echo ❌ Cannot create directory in Program Files
    echo Make sure you're running as administrator
) else (
    echo ✅ Can create directories in Program Files
    rmdir "%TEST_DIR%" >nul 2>&1
)
echo.

echo 6. Checking PowerShell execution policy...
powershell -Command "Get-ExecutionPolicy" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell execution restricted
    echo This may prevent desktop shortcut creation
) else (
    echo ✅ PowerShell available
    powershell -Command "Write-Host 'PowerShell test successful'"
)
echo.

echo 7. Summary:
echo ========================================
if exist "dist\WiFiOptimizer.exe" (
    echo ✅ Application built and ready
) else (
    echo ❌ Need to build application first
)

dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Need to install .NET 6.0
) else (
    echo ✅ .NET 6.0 ready
)

net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Need administrator privileges
) else (
    echo ✅ Administrator privileges OK
)

echo.
echo If all items show ✅, you can run install.bat
echo If any show ❌, fix those issues first
echo.
pause
