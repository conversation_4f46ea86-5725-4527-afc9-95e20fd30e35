<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <AssemblyTitle>WiFi Adapter Optimizer</AssemblyTitle>
    <AssemblyDescription>24/7 WiFi Adapter Speed Optimizer</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>WiFi Optimizer</Company>
    <Product>WiFi Adapter Optimizer</Product>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="7.0.1" />
    <PackageReference Include="System.Windows.Forms.DataVisualization" Version="1.0.0-prerelease.20110.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>



</Project>
