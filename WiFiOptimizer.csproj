<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>wifi-icon.ico</ApplicationIcon>
    <AssemblyTitle>WiFi Adapter Optimizer</AssemblyTitle>
    <AssemblyDescription>24/7 WiFi Adapter Speed Optimizer</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>WiFi Optimizer</Company>
    <Product>WiFi Adapter Optimizer</Product>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="7.0.1" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\wifi-connected.ico" />
    <EmbeddedResource Include="Resources\wifi-disconnected.ico" />
  </ItemGroup>

</Project>
