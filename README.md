# WiFi Adapter Optimizer

A lightweight Windows application that runs 24/7 to optimize your WiFi adapter settings for maximum speed and performance.

## Features

- **24/7 Background Operation** - Runs silently in system tray
- **Automatic WiFi Optimization** - Continuously optimizes adapter settings
- **Minimal Resource Usage** - Uses less than 10MB RAM
- **Auto-Start with Windows** - Automatically starts when Windows boots
- **Real-time Monitoring** - Monitors WiFi connection status
- **One-Click Control** - Enable/disable optimization from tray menu

## What It Optimizes

### WiFi Adapter Settings
- Disables power management (prevents adapter sleep)
- Sets maximum transmit power
- Optimizes channel width and wireless mode
- Enables throughput booster features
- Reduces unnecessary background scanning

### Network Stack
- Optimizes TCP/IP parameters
- Improves packet handling
- Reduces network latency
- Enhances data throughput

## Installation

1. **Download .NET 6.0** (if not already installed):
   - Visit: https://dotnet.microsoft.com/download/dotnet/6.0
   - Download and install .NET 6.0 Desktop Runtime

2. **Build the Application**:
   ```cmd
   build.bat
   ```

3. **Run the Optimizer**:
   ```cmd
   dist\WiFiOptimizer.exe
   ```

## Usage

### First Run
- Double-click `WiFiOptimizer.exe`
- The application will start in the system tray (bottom-right corner)
- Look for a green/red circle icon
- Green = WiFi connected and optimized
- Red = WiFi disconnected or optimization disabled

### System Tray Menu
Right-click the tray icon to access:
- **Enable/Disable Optimization** - Toggle optimization on/off
- **Status** - View current optimization status
- **Settings** - Configuration options (coming soon)
- **Exit** - Close the application

### Auto-Start
The application automatically configures itself to start with Windows. No manual setup required.

## System Requirements

- Windows 10/11
- .NET 6.0 Runtime
- Administrator privileges (for adapter optimization)
- WiFi adapter (802.11n/ac/ax recommended)

## Technical Details

### Memory Usage
- Typical RAM usage: 5-8MB
- CPU usage: <1% (periodic checks every 30 seconds)
- Disk usage: ~15MB (self-contained executable)

### Optimization Frequency
- Initial optimization on startup
- Re-optimization every 30 seconds
- Immediate optimization when WiFi reconnects

### Registry Changes
The application modifies WiFi adapter settings in the Windows registry:
- Power management settings
- Transmit power levels
- Channel and roaming configurations
- TCP/IP stack parameters

**Note**: All changes are standard Windows networking optimizations and are safe.

## Troubleshooting

### Application Won't Start
- Ensure .NET 6.0 is installed
- Run as Administrator if needed
- Check Windows Event Viewer for errors

### No Optimization Effect
- Verify WiFi adapter supports advanced settings
- Check if other WiFi management software is running
- Restart the application after driver updates

### High CPU Usage
- This indicates a problem - normal usage should be <1%
- Restart the application
- Check for Windows updates

## Building from Source

### Prerequisites
- .NET 6.0 SDK
- Windows 10/11
- Visual Studio 2022 (optional)

### Build Commands
```cmd
# Restore packages
dotnet restore

# Build debug version
dotnet build

# Build release version
dotnet build --configuration Release

# Create executable
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output dist -p:PublishSingleFile=true
```

## License

This software is provided as-is for educational and personal use.

## Disclaimer

This application modifies Windows network settings to optimize WiFi performance. While all changes are standard networking optimizations, use at your own discretion. The authors are not responsible for any system issues that may arise.
