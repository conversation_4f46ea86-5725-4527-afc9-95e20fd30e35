@echo off
echo WiFi Adapter Optimizer - Uninstall Script
echo ==========================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please run as Administrator.
    echo.
    echo Right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo This will completely remove WiFi Optimizer from your system.
echo.
echo Are you sure you want to uninstall? (Y/N)
set /p confirm=
if /i not "%confirm%"=="Y" (
    echo Uninstall cancelled.
    pause
    exit /b 0
)

echo.
echo Uninstalling WiFi Optimizer...

REM Stop the application if running
echo Stopping WiFi Optimizer...
taskkill /f /im WiFiOptimizer.exe >nul 2>&1

REM Remove from startup
echo Removing auto-start...
reg delete "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "WiFiOptimizer" /f >nul 2>&1

REM Remove desktop shortcut
set DESKTOP=%USERPROFILE%\Desktop
if exist "%DESKTOP%\WiFi Optimizer.lnk" (
    echo Removing desktop shortcut...
    del "%DESKTOP%\WiFi Optimizer.lnk" >nul
)

REM Remove installation directory
set INSTALL_DIR=%ProgramFiles%\WiFiOptimizer
if exist "%INSTALL_DIR%" (
    echo Removing installation files...
    rmdir /s /q "%INSTALL_DIR%" >nul 2>&1
)

REM Clean up any remaining registry entries (optional)
echo Cleaning up registry...
reg delete "HKCU\SOFTWARE\WiFiOptimizer" /f >nul 2>&1

echo.
echo ==========================================
echo Uninstall completed successfully!
echo ==========================================
echo.
echo The following items have been removed:
echo - WiFi Optimizer application
echo - Desktop shortcut
echo - Auto-start configuration
echo - Installation files
echo.
echo Note: Your WiFi adapter settings have been left as-is.
echo If you want to reset them to defaults, you can:
echo 1. Go to Device Manager
echo 2. Find your WiFi adapter
echo 3. Right-click ^> Properties ^> Power Management
echo 4. Check "Allow computer to turn off this device"
echo.
echo Thank you for using WiFi Optimizer!
pause
