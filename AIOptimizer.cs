using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using Newtonsoft.Json;

namespace WiFiOptimizer
{
    public class AIOptimizer
    {
        private readonly List<NetworkPattern> patterns;
        private readonly List<OptimizationAction> actions;
        private readonly string dataFile;
        private DateTime lastLearning;

        public AIOptimizer()
        {
            patterns = new List<NetworkPattern>();
            actions = new List<OptimizationAction>();
            dataFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "WiFiOptimizer", "ai_data.json");
            LoadLearningData();
        }

        public async Task<OptimizationRecommendation> AnalyzeAndOptimize(NetworkUsageData currentUsage, List<SpeedTestResult> speedHistory)
        {
            var recommendation = new OptimizationRecommendation();

            try
            {
                // 1. Pattern Recognition
                var detectedPattern = DetectUsagePattern(currentUsage, speedHistory);
                
                // 2. Predictive Analysis
                var prediction = PredictNetworkNeeds(detectedPattern);
                
                // 3. Generate Optimization Strategy
                recommendation = GenerateOptimizationStrategy(detectedPattern, prediction);
                
                // 4. <PERSON>rn from current session
                await LearnFromCurrentSession(currentUsage, speedHistory);
                
                // 5. Update AI model
                UpdateAIModel(detectedPattern, recommendation);

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AI Optimization error: {ex.Message}");
            }

            return recommendation;
        }

        private NetworkPattern DetectUsagePattern(NetworkUsageData currentUsage, List<SpeedTestResult> speedHistory)
        {
            var pattern = new NetworkPattern
            {
                Timestamp = DateTime.Now,
                TimeOfDay = DateTime.Now.Hour,
                DayOfWeek = (int)DateTime.Now.DayOfWeek,
                AverageDownloadSpeed = speedHistory.Count > 0 ? speedHistory.Average(s => s.DownloadSpeed) : 0,
                AverageUploadSpeed = speedHistory.Count > 0 ? speedHistory.Average(s => s.UploadSpeed) : 0,
                AveragePing = speedHistory.Count > 0 ? speedHistory.Average(s => s.Ping) : 0,
                CurrentDownloadUsage = currentUsage.DownloadMbps,
                CurrentUploadUsage = currentUsage.UploadMbps
            };

            // Detect activity type based on usage patterns
            pattern.ActivityType = DetectActivityType(pattern);
            
            // Calculate stability score
            pattern.StabilityScore = CalculateStabilityScore(speedHistory);

            return pattern;
        }

        private ActivityType DetectActivityType(NetworkPattern pattern)
        {
            // AI-based activity detection
            if (pattern.CurrentUploadUsage > 5 && pattern.CurrentDownloadUsage > 10)
                return ActivityType.VideoCall;
            
            if (pattern.CurrentDownloadUsage > 25)
                return ActivityType.Streaming;
            
            if (pattern.AveragePing < 20 && pattern.CurrentDownloadUsage < 5)
                return ActivityType.Gaming;
            
            if (pattern.CurrentDownloadUsage > 50)
                return ActivityType.Downloading;
            
            if (pattern.CurrentUploadUsage > 10)
                return ActivityType.Uploading;
            
            return ActivityType.Browsing;
        }

        private double CalculateStabilityScore(List<SpeedTestResult> speedHistory)
        {
            if (speedHistory.Count < 2) return 1.0;

            var downloadVariance = CalculateVariance(speedHistory.Select(s => s.DownloadSpeed));
            var pingVariance = CalculateVariance(speedHistory.Select(s => s.Ping));
            
            // Lower variance = higher stability
            return Math.Max(0, 1.0 - (downloadVariance + pingVariance) / 100.0);
        }

        private double CalculateVariance(IEnumerable<double> values)
        {
            var valueList = values.ToList();
            if (valueList.Count < 2) return 0;
            
            var mean = valueList.Average();
            return valueList.Average(v => Math.Pow(v - mean, 2));
        }

        private NetworkPrediction PredictNetworkNeeds(NetworkPattern pattern)
        {
            var prediction = new NetworkPrediction();

            // Find similar historical patterns
            var similarPatterns = patterns.Where(p => 
                Math.Abs(p.TimeOfDay - pattern.TimeOfDay) <= 2 &&
                p.DayOfWeek == pattern.DayOfWeek &&
                p.ActivityType == pattern.ActivityType
            ).ToList();

            if (similarPatterns.Any())
            {
                prediction.PredictedDownloadNeed = similarPatterns.Average(p => p.AverageDownloadSpeed);
                prediction.PredictedUploadNeed = similarPatterns.Average(p => p.AverageUploadSpeed);
                prediction.PredictedPingRequirement = similarPatterns.Average(p => p.AveragePing);
                prediction.Confidence = Math.Min(1.0, similarPatterns.Count / 10.0);
            }
            else
            {
                // Fallback predictions based on activity type
                prediction = GetDefaultPredictionForActivity(pattern.ActivityType);
                prediction.Confidence = 0.3; // Lower confidence for default predictions
            }

            return prediction;
        }

        private NetworkPrediction GetDefaultPredictionForActivity(ActivityType activity)
        {
            return activity switch
            {
                ActivityType.Gaming => new NetworkPrediction { PredictedDownloadNeed = 10, PredictedUploadNeed = 5, PredictedPingRequirement = 15 },
                ActivityType.Streaming => new NetworkPrediction { PredictedDownloadNeed = 50, PredictedUploadNeed = 5, PredictedPingRequirement = 50 },
                ActivityType.VideoCall => new NetworkPrediction { PredictedDownloadNeed = 15, PredictedUploadNeed = 15, PredictedPingRequirement = 30 },
                ActivityType.Downloading => new NetworkPrediction { PredictedDownloadNeed = 100, PredictedUploadNeed = 5, PredictedPingRequirement = 100 },
                ActivityType.Uploading => new NetworkPrediction { PredictedDownloadNeed = 10, PredictedUploadNeed = 50, PredictedPingRequirement = 50 },
                _ => new NetworkPrediction { PredictedDownloadNeed = 20, PredictedUploadNeed = 5, PredictedPingRequirement = 50 }
            };
        }

        private OptimizationRecommendation GenerateOptimizationStrategy(NetworkPattern pattern, NetworkPrediction prediction)
        {
            var recommendation = new OptimizationRecommendation
            {
                Timestamp = DateTime.Now,
                DetectedActivity = pattern.ActivityType,
                Confidence = prediction.Confidence,
                Actions = new List<string>()
            };

            // Generate specific recommendations based on AI analysis
            if (pattern.StabilityScore < 0.7)
            {
                recommendation.Actions.Add("🔧 Enable connection stability mode");
                recommendation.Actions.Add("📡 Switch to 5GHz band for better stability");
            }

            if (pattern.AveragePing > prediction.PredictedPingRequirement * 1.5)
            {
                recommendation.Actions.Add("⚡ Enable gaming mode for lower latency");
                recommendation.Actions.Add("🎯 Optimize TCP settings for ping reduction");
            }

            if (pattern.AverageDownloadSpeed < prediction.PredictedDownloadNeed * 0.8)
            {
                recommendation.Actions.Add("🚀 Enable turbo mode for maximum throughput");
                recommendation.Actions.Add("📶 Optimize channel selection");
            }

            // Activity-specific optimizations
            switch (pattern.ActivityType)
            {
                case ActivityType.Gaming:
                    recommendation.Actions.Add("🎮 Activate gaming mode with ultra-low latency");
                    recommendation.Actions.Add("🔥 Disable background updates and sync");
                    break;
                case ActivityType.Streaming:
                    recommendation.Actions.Add("📺 Enable streaming optimization mode");
                    recommendation.Actions.Add("📊 Prioritize download bandwidth");
                    break;
                case ActivityType.VideoCall:
                    recommendation.Actions.Add("📹 Balance upload/download optimization");
                    recommendation.Actions.Add("🔇 Minimize jitter and packet loss");
                    break;
            }

            recommendation.Summary = GenerateAISummary(pattern, prediction, recommendation.Actions.Count);
            
            return recommendation;
        }

        private string GenerateAISummary(NetworkPattern pattern, NetworkPrediction prediction, int actionCount)
        {
            var activity = pattern.ActivityType.ToString().ToLower();
            var confidence = (prediction.Confidence * 100).ToString("F0");
            
            return $"🤖 AI detected {activity} activity with {confidence}% confidence. " +
                   $"Applied {actionCount} optimizations for your usage pattern. " +
                   $"Network stability: {(pattern.StabilityScore * 100):F0}%";
        }

        private async Task LearnFromCurrentSession(NetworkUsageData currentUsage, List<SpeedTestResult> speedHistory)
        {
            // Learn every 10 minutes
            if (DateTime.Now - lastLearning < TimeSpan.FromMinutes(10)) return;

            try
            {
                var pattern = DetectUsagePattern(currentUsage, speedHistory);
                patterns.Add(pattern);

                // Keep only recent patterns (last 30 days)
                var cutoff = DateTime.Now.AddDays(-30);
                patterns.RemoveAll(p => p.Timestamp < cutoff);

                lastLearning = DateTime.Now;
                await SaveLearningData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Learning error: {ex.Message}");
            }
        }

        private void UpdateAIModel(NetworkPattern pattern, OptimizationRecommendation recommendation)
        {
            // Record the action taken for future learning
            var action = new OptimizationAction
            {
                Timestamp = DateTime.Now,
                Pattern = pattern,
                Recommendation = recommendation,
                Success = true // This would be measured by performance improvement
            };

            actions.Add(action);

            // Keep only recent actions
            var cutoff = DateTime.Now.AddDays(-7);
            actions.RemoveAll(a => a.Timestamp < cutoff);
        }

        private async Task SaveLearningData()
        {
            try
            {
                var data = new AILearningData
                {
                    Patterns = patterns,
                    Actions = actions,
                    LastUpdated = DateTime.Now
                };

                Directory.CreateDirectory(Path.GetDirectoryName(dataFile)!);
                var json = JsonConvert.SerializeObject(data, Formatting.Indented);
                await File.WriteAllTextAsync(dataFile, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Save learning data error: {ex.Message}");
            }
        }

        private void LoadLearningData()
        {
            try
            {
                if (File.Exists(dataFile))
                {
                    var json = File.ReadAllText(dataFile);
                    var data = JsonConvert.DeserializeObject<AILearningData>(json);
                    
                    if (data != null)
                    {
                        patterns.AddRange(data.Patterns ?? new List<NetworkPattern>());
                        actions.AddRange(data.Actions ?? new List<OptimizationAction>());
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Load learning data error: {ex.Message}");
            }
        }

        public string GetAIStatus()
        {
            var patternsCount = patterns.Count;
            var actionsCount = actions.Count;
            var learningAge = patterns.Any() ? (DateTime.Now - patterns.Max(p => p.Timestamp)).Days : 0;

            return $"🤖 AI Learning Status:\n" +
                   $"📊 Patterns Learned: {patternsCount}\n" +
                   $"⚡ Actions Recorded: {actionsCount}\n" +
                   $"🧠 Learning Age: {learningAge} days\n" +
                   $"🎯 AI Confidence: {(patternsCount > 50 ? "High" : patternsCount > 20 ? "Medium" : "Learning")}";
        }
    }

    public enum ActivityType
    {
        Browsing,
        Gaming,
        Streaming,
        VideoCall,
        Downloading,
        Uploading,
        Unknown
    }

    public class NetworkPattern
    {
        public DateTime Timestamp { get; set; }
        public int TimeOfDay { get; set; }
        public int DayOfWeek { get; set; }
        public double AverageDownloadSpeed { get; set; }
        public double AverageUploadSpeed { get; set; }
        public double AveragePing { get; set; }
        public double CurrentDownloadUsage { get; set; }
        public double CurrentUploadUsage { get; set; }
        public ActivityType ActivityType { get; set; }
        public double StabilityScore { get; set; }
    }

    public class NetworkPrediction
    {
        public double PredictedDownloadNeed { get; set; }
        public double PredictedUploadNeed { get; set; }
        public double PredictedPingRequirement { get; set; }
        public double Confidence { get; set; }
    }

    public class OptimizationRecommendation
    {
        public DateTime Timestamp { get; set; }
        public ActivityType DetectedActivity { get; set; }
        public double Confidence { get; set; }
        public List<string> Actions { get; set; } = new List<string>();
        public string Summary { get; set; } = "";
    }

    public class OptimizationAction
    {
        public DateTime Timestamp { get; set; }
        public NetworkPattern Pattern { get; set; } = new NetworkPattern();
        public OptimizationRecommendation Recommendation { get; set; } = new OptimizationRecommendation();
        public bool Success { get; set; }
    }

    public class AILearningData
    {
        public List<NetworkPattern> Patterns { get; set; } = new List<NetworkPattern>();
        public List<OptimizationAction> Actions { get; set; } = new List<OptimizationAction>();
        public DateTime LastUpdated { get; set; }
    }
}
