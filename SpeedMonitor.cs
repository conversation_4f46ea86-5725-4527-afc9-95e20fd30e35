using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Timers;
using Newtonsoft.Json;

namespace WiFiOptimizer
{
    public class SpeedMonitor
    {
        private readonly HttpClient httpClient;
        private readonly System.Timers.Timer speedTestTimer;
        private readonly List<SpeedTestResult> speedHistory;
        private readonly PerformanceCounter downloadCounter;
        private readonly PerformanceCounter uploadCounter;
        
        public event EventHandler<SpeedTestResult>? SpeedTestCompleted;
        public event EventHandler<NetworkUsageData>? NetworkUsageUpdated;

        public SpeedMonitor()
        {
            httpClient = new HttpClient() { Timeout = TimeSpan.FromSeconds(30) };
            speedHistory = new List<SpeedTestResult>();
            
            // Initialize performance counters for real-time monitoring
            downloadCounter = new PerformanceCounter("Network Interface", "Bytes Received/sec", GetNetworkInterfaceName());
            uploadCounter = new PerformanceCounter("Network Interface", "Bytes Sent/sec", GetNetworkInterfaceName());
            
            // Timer for periodic speed tests (every 5 minutes)
            speedTestTimer = new System.Timers.Timer(300000); // 5 minutes
            speedTestTimer.Elapsed += OnSpeedTestTimer;
            speedTestTimer.AutoReset = true;
        }

        public void StartMonitoring()
        {
            speedTestTimer.Start();
            StartRealtimeMonitoring();
        }

        public void StopMonitoring()
        {
            speedTestTimer.Stop();
        }

        private async void OnSpeedTestTimer(object? sender, ElapsedEventArgs e)
        {
            try
            {
                var result = await PerformSpeedTest();
                speedHistory.Add(result);
                
                // Keep only last 24 hours of data
                if (speedHistory.Count > 288) // 24 hours * 12 tests per hour
                {
                    speedHistory.RemoveAt(0);
                }
                
                SpeedTestCompleted?.Invoke(this, result);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Speed test error: {ex.Message}");
            }
        }

        public async Task<SpeedTestResult> PerformSpeedTest()
        {
            var result = new SpeedTestResult
            {
                Timestamp = DateTime.Now,
                Ping = await MeasurePing(),
                DownloadSpeed = await MeasureDownloadSpeed(),
                UploadSpeed = await MeasureUploadSpeed()
            };

            return result;
        }

        private async Task<double> MeasurePing()
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync("*******", 5000);
                return reply.Status == IPStatus.Success ? reply.RoundtripTime : -1;
            }
            catch
            {
                return -1;
            }
        }

        private async Task<double> MeasureDownloadSpeed()
        {
            try
            {
                // Test with multiple servers for accuracy
                var testUrls = new[]
                {
                    "http://speedtest.ftp.otenet.gr/files/test10Mb.db",
                    "http://ipv4.download.thinkbroadband.com/10MB.zip",
                    "https://proof.ovh.net/files/10Mb.dat"
                };

                double totalSpeed = 0;
                int successfulTests = 0;

                foreach (var url in testUrls)
                {
                    try
                    {
                        var stopwatch = Stopwatch.StartNew();
                        var response = await httpClient.GetAsync(url);
                        var data = await response.Content.ReadAsByteArrayAsync();
                        stopwatch.Stop();

                        if (data.Length > 0)
                        {
                            double speedMbps = (data.Length * 8.0) / (stopwatch.ElapsedMilliseconds / 1000.0) / 1_000_000;
                            totalSpeed += speedMbps;
                            successfulTests++;
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }

                return successfulTests > 0 ? totalSpeed / successfulTests : 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<double> MeasureUploadSpeed()
        {
            try
            {
                // Create test data (1MB)
                var testData = new byte[1024 * 1024];
                new Random().NextBytes(testData);

                var stopwatch = Stopwatch.StartNew();
                var content = new ByteArrayContent(testData);
                
                // Use httpbin.org for upload testing
                await httpClient.PostAsync("https://httpbin.org/post", content);
                stopwatch.Stop();

                double speedMbps = (testData.Length * 8.0) / (stopwatch.ElapsedMilliseconds / 1000.0) / 1_000_000;
                return speedMbps;
            }
            catch
            {
                return 0;
            }
        }

        private void StartRealtimeMonitoring()
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        var usage = new NetworkUsageData
                        {
                            Timestamp = DateTime.Now,
                            DownloadBytesPerSec = downloadCounter.NextValue(),
                            UploadBytesPerSec = uploadCounter.NextValue()
                        };

                        NetworkUsageUpdated?.Invoke(this, usage);
                        await Task.Delay(1000); // Update every second
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Real-time monitoring error: {ex.Message}");
                        await Task.Delay(5000); // Wait 5 seconds on error
                    }
                }
            });
        }

        private string GetNetworkInterfaceName()
        {
            try
            {
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in interfaces)
                {
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211 && 
                        ni.OperationalStatus == OperationalStatus.Up)
                    {
                        return ni.Description;
                    }
                }
                return "_Total"; // Fallback to total network usage
            }
            catch
            {
                return "_Total";
            }
        }

        public List<SpeedTestResult> GetSpeedHistory() => new List<SpeedTestResult>(speedHistory);

        public SpeedTestResult? GetLatestSpeedTest() => 
            speedHistory.Count > 0 ? speedHistory[speedHistory.Count - 1] : null;

        public void Dispose()
        {
            speedTestTimer?.Dispose();
            httpClient?.Dispose();
            downloadCounter?.Dispose();
            uploadCounter?.Dispose();
        }
    }

    public class SpeedTestResult
    {
        public DateTime Timestamp { get; set; }
        public double Ping { get; set; } // milliseconds
        public double DownloadSpeed { get; set; } // Mbps
        public double UploadSpeed { get; set; } // Mbps
        
        public string GetSummary() => 
            $"⬇️ {DownloadSpeed:F1} Mbps | ⬆️ {UploadSpeed:F1} Mbps | 📶 {Ping:F0}ms";
    }

    public class NetworkUsageData
    {
        public DateTime Timestamp { get; set; }
        public float DownloadBytesPerSec { get; set; }
        public float UploadBytesPerSec { get; set; }
        
        public double DownloadMbps => (DownloadBytesPerSec * 8) / 1_000_000;
        public double UploadMbps => (UploadBytesPerSec * 8) / 1_000_000;
    }
}
