using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace WiFiOptimizer
{
    public partial class DashboardForm : Form
    {
        private readonly SpeedMonitor speedMonitor;
        private readonly GameModeOptimizer gameModeOptimizer;
        private readonly AIOptimizer aiOptimizer;
        private System.Windows.Forms.Timer refreshTimer;

        private Label speedLabel;
        private Label usageLabel;
        private Label gameModeLabel;
        private Label aiStatusLabel;
        private ProgressBar downloadBar;
        private ProgressBar uploadBar;
        private ProgressBar pingBar;
        private ListBox historyListBox;

        public DashboardForm(SpeedMonitor speedMonitor, GameModeOptimizer gameModeOptimizer, AIOptimizer aiOptimizer)
        {
            this.speedMonitor = speedMonitor;
            this.gameModeOptimizer = gameModeOptimizer;
            this.aiOptimizer = aiOptimizer;
            
            InitializeComponent();
            SetupRefreshTimer();
            RefreshData();
        }

        private void InitializeComponent()
        {
            this.Text = "📊 WiFi Optimizer Dashboard";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);

            // Title
            var titleLabel = new Label
            {
                Text = "🚀 ENHANCED WIFI OPTIMIZER DASHBOARD",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 120, 215),
                Location = new Point(20, 20),
                Size = new Size(560, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(titleLabel);

            // Speed Test Section
            var speedGroupBox = new GroupBox
            {
                Text = "⚡ Speed Test Results",
                Location = new Point(20, 60),
                Size = new Size(270, 150),
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            speedLabel = new Label
            {
                Text = "No speed test data",
                Location = new Point(10, 25),
                Size = new Size(250, 20),
                Font = new Font("Segoe UI", 9)
            };

            downloadBar = new ProgressBar
            {
                Location = new Point(10, 50),
                Size = new Size(200, 20),
                Maximum = 100
            };

            uploadBar = new ProgressBar
            {
                Location = new Point(10, 75),
                Size = new Size(200, 20),
                Maximum = 50
            };

            pingBar = new ProgressBar
            {
                Location = new Point(10, 100),
                Size = new Size(200, 20),
                Maximum = 100
            };

            var downloadLabel = new Label { Text = "⬇️ Download", Location = new Point(215, 50), Size = new Size(50, 20) };
            var uploadLabel = new Label { Text = "⬆️ Upload", Location = new Point(215, 75), Size = new Size(50, 20) };
            var pingLabel = new Label { Text = "📶 Ping", Location = new Point(215, 100), Size = new Size(50, 20) };

            speedGroupBox.Controls.AddRange(new Control[] { speedLabel, downloadBar, uploadBar, pingBar, downloadLabel, uploadLabel, pingLabel });
            this.Controls.Add(speedGroupBox);

            // Real-time Usage Section
            var usageGroupBox = new GroupBox
            {
                Text = "📊 Real-time Usage",
                Location = new Point(310, 60),
                Size = new Size(270, 150),
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            usageLabel = new Label
            {
                Text = "Monitoring network usage...",
                Location = new Point(10, 25),
                Size = new Size(250, 100),
                Font = new Font("Segoe UI", 9)
            };

            usageGroupBox.Controls.Add(usageLabel);
            this.Controls.Add(usageGroupBox);

            // Gaming Mode Section
            var gamingGroupBox = new GroupBox
            {
                Text = "🎮 Gaming Mode",
                Location = new Point(20, 230),
                Size = new Size(270, 100),
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            gameModeLabel = new Label
            {
                Text = "Gaming mode status...",
                Location = new Point(10, 25),
                Size = new Size(250, 60),
                Font = new Font("Segoe UI", 9)
            };

            gamingGroupBox.Controls.Add(gameModeLabel);
            this.Controls.Add(gamingGroupBox);

            // AI Status Section
            var aiGroupBox = new GroupBox
            {
                Text = "🤖 AI Optimizer",
                Location = new Point(310, 230),
                Size = new Size(270, 100),
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            aiStatusLabel = new Label
            {
                Text = "AI status loading...",
                Location = new Point(10, 25),
                Size = new Size(250, 60),
                Font = new Font("Segoe UI", 9)
            };

            aiGroupBox.Controls.Add(aiStatusLabel);
            this.Controls.Add(aiGroupBox);

            // History Section
            var historyGroupBox = new GroupBox
            {
                Text = "📈 Speed Test History",
                Location = new Point(20, 350),
                Size = new Size(560, 100),
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            historyListBox = new ListBox
            {
                Location = new Point(10, 25),
                Size = new Size(540, 65),
                Font = new Font("Consolas", 8)
            };

            historyGroupBox.Controls.Add(historyListBox);
            this.Controls.Add(historyGroupBox);

            // Buttons
            var refreshButton = new Button
            {
                Text = "🔄 Refresh",
                Location = new Point(400, 460),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            refreshButton.Click += (s, e) => RefreshData();

            var closeButton = new Button
            {
                Text = "❌ Close",
                Location = new Point(500, 460),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            closeButton.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] { refreshButton, closeButton });
        }

        private void SetupRefreshTimer()
        {
            refreshTimer = new System.Windows.Forms.Timer();
            refreshTimer.Interval = 2000; // Refresh every 2 seconds
            refreshTimer.Tick += (s, e) => RefreshData();
            refreshTimer.Start();
        }

        private void RefreshData()
        {
            try
            {
                // Update speed test data
                var latestSpeed = speedMonitor.GetLatestSpeedTest();
                if (latestSpeed != null)
                {
                    speedLabel.Text = $"Latest: {latestSpeed.GetSummary()}\nTested: {latestSpeed.Timestamp:HH:mm:ss}";
                    
                    downloadBar.Value = Math.Min(100, (int)latestSpeed.DownloadSpeed);
                    uploadBar.Value = Math.Min(50, (int)latestSpeed.UploadSpeed);
                    pingBar.Value = Math.Min(100, 100 - (int)Math.Min(100, latestSpeed.Ping));
                }

                // Update real-time usage (this would need to be implemented in SpeedMonitor)
                usageLabel.Text = "📊 Real-time monitoring active\n" +
                                "⬇️ Download: Monitoring...\n" +
                                "⬆️ Upload: Monitoring...\n" +
                                "🕒 Updated: " + DateTime.Now.ToString("HH:mm:ss");

                // Update gaming mode status
                gameModeLabel.Text = gameModeOptimizer.GetGameModeStatus();

                // Update AI status
                aiStatusLabel.Text = aiOptimizer.GetAIStatus().Replace("🤖 AI Learning Status:\n", "").Replace("\n", "\n");

                // Update history
                var history = speedMonitor.GetSpeedHistory().TakeLast(5).Reverse();
                historyListBox.Items.Clear();
                foreach (var test in history)
                {
                    historyListBox.Items.Add($"{test.Timestamp:HH:mm:ss} | {test.GetSummary()}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Dashboard refresh error: {ex.Message}");
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            refreshTimer?.Stop();
            refreshTimer?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
