using System;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using Microsoft.Win32;

namespace WiFiOptimizer
{
    public class GameModeOptimizer
    {
        [DllImport("kernel32.dll")]
        private static extern bool SetPriorityClass(IntPtr hProcess, uint dwPriorityClass);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        private const uint HIGH_PRIORITY_CLASS = 0x00000080;
        private const uint REALTIME_PRIORITY_CLASS = 0x00000100;

        private bool isGameModeActive = false;
        private RegistryKey? tcpipKey;

        public bool IsGameModeActive => isGameModeActive;

        public void EnableGameMode()
        {
            if (isGameModeActive) return;

            try
            {
                // 1. Set process priority to high
                SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);

                // 2. Optimize TCP settings for gaming
                OptimizeTcpForGaming();

                // 3. Disable Windows Game Mode interference
                DisableWindowsGameMode();

                // 4. Optimize network adapter for ultra-low latency
                OptimizeAdapterForGaming();

                // 5. Set CPU affinity for network interrupts
                OptimizeCpuAffinity();

                // 6. Disable network throttling
                DisableNetworkThrottling();

                isGameModeActive = true;
                Debug.WriteLine("🎮 GAME MODE ACTIVATED - Ultra Low Latency Enabled!");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Game mode activation error: {ex.Message}");
            }
        }

        public void DisableGameMode()
        {
            if (!isGameModeActive) return;

            try
            {
                // Restore normal TCP settings
                RestoreNormalTcpSettings();

                // Restore normal process priority
                SetPriorityClass(GetCurrentProcess(), 0x00000020); // NORMAL_PRIORITY_CLASS

                // Re-enable network throttling
                EnableNetworkThrottling();

                isGameModeActive = false;
                Debug.WriteLine("🎮 Game Mode Deactivated - Normal Settings Restored");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Game mode deactivation error: {ex.Message}");
            }
        }

        private void OptimizeTcpForGaming()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true);
                if (key != null)
                {
                    // Ultra-low latency TCP settings
                    key.SetValue("TcpAckFrequency", 1, RegistryValueKind.DWord);
                    key.SetValue("TCPNoDelay", 1, RegistryValueKind.DWord);
                    key.SetValue("TcpDelAckTicks", 0, RegistryValueKind.DWord);
                    key.SetValue("TcpTimedWaitDelay", 30, RegistryValueKind.DWord);
                    key.SetValue("MaxConnectionsPerServer", 16, RegistryValueKind.DWord);
                    key.SetValue("MaxConnectionsPer1_0Server", 16, RegistryValueKind.DWord);
                    
                    // Gaming-specific optimizations
                    key.SetValue("DefaultTTL", 64, RegistryValueKind.DWord);
                    key.SetValue("EnablePMTUBHDetect", 0, RegistryValueKind.DWord);
                    key.SetValue("EnablePMTUDiscovery", 1, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TCP gaming optimization error: {ex.Message}");
            }
        }

        private void DisableWindowsGameMode()
        {
            try
            {
                // Disable Windows 10/11 Game Mode that can interfere
                using var key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\GameBar", true);
                if (key != null)
                {
                    key.SetValue("AllowAutoGameMode", 0, RegistryValueKind.DWord);
                    key.SetValue("AutoGameModeEnabled", 0, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Windows Game Mode disable error: {ex.Message}");
            }
        }

        private void OptimizeAdapterForGaming()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionStatus=2 AND AdapterTypeId=9");

                foreach (ManagementObject adapter in searcher.Get())
                {
                    string? deviceId = adapter["DeviceID"]?.ToString();
                    if (deviceId != null)
                    {
                        SetGamingAdapterSettings(deviceId);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Adapter gaming optimization error: {ex.Message}");
            }
        }

        private void SetGamingAdapterSettings(string deviceId)
        {
            try
            {
                string registryPath = $@"SYSTEM\CurrentControlSet\Control\Class\{{4d36e972-e325-11ce-bfc1-08002be10318}}\{deviceId.PadLeft(4, '0')}";
                
                using var key = Registry.LocalMachine.OpenSubKey(registryPath, true);
                if (key != null)
                {
                    // Ultra-low latency gaming settings
                    key.SetValue("InterruptModeration", "0", RegistryValueKind.String); // Disable interrupt moderation
                    key.SetValue("TxIntDelay", "0", RegistryValueKind.String); // Minimum transmit delay
                    key.SetValue("RxIntDelay", "0", RegistryValueKind.String); // Minimum receive delay
                    key.SetValue("PacketCoalescing", "0", RegistryValueKind.String); // Disable packet coalescing
                    key.SetValue("EnableGreenEthernet", "0", RegistryValueKind.String); // Disable power saving
                    key.SetValue("ReduceSpeedOnPowerDown", "0", RegistryValueKind.String);
                    key.SetValue("EnablePowerManagement", "0", RegistryValueKind.String);
                    
                    // Gaming-specific optimizations
                    key.SetValue("FlowControl", "0", RegistryValueKind.String); // Disable flow control
                    key.SetValue("AdaptiveIFS", "0", RegistryValueKind.String); // Disable adaptive IFS
                    key.SetValue("FatChannelIntolerant", "0", RegistryValueKind.String);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Gaming adapter settings error: {ex.Message}");
            }
        }

        private void OptimizeCpuAffinity()
        {
            try
            {
                // Set network interrupts to specific CPU cores for better performance
                using var key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Session Manager\kernel", true);
                if (key != null)
                {
                    key.SetValue("DpcWatchdogProfileOffset", 1, RegistryValueKind.DWord);
                    key.SetValue("DisableLowQosTimerResolution", 1, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"CPU affinity optimization error: {ex.Message}");
            }
        }

        private void DisableNetworkThrottling()
        {
            try
            {
                // Disable Windows network throttling for gaming
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile", true);
                if (key != null)
                {
                    key.SetValue("NetworkThrottlingIndex", 0xffffffff, RegistryValueKind.DWord);
                    key.SetValue("SystemResponsiveness", 0, RegistryValueKind.DWord);
                }

                // Gaming-specific network profile
                using var gameKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games", true);
                if (gameKey != null)
                {
                    gameKey.SetValue("GPU Priority", 8, RegistryValueKind.DWord);
                    gameKey.SetValue("Priority", 6, RegistryValueKind.DWord);
                    gameKey.SetValue("Scheduling Category", "High", RegistryValueKind.String);
                    gameKey.SetValue("SFIO Priority", "High", RegistryValueKind.String);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Network throttling disable error: {ex.Message}");
            }
        }

        private void RestoreNormalTcpSettings()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true);
                if (key != null)
                {
                    // Restore default values
                    key.SetValue("TcpAckFrequency", 2, RegistryValueKind.DWord);
                    key.SetValue("TcpDelAckTicks", 2, RegistryValueKind.DWord);
                    key.SetValue("TcpTimedWaitDelay", 240, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TCP settings restore error: {ex.Message}");
            }
        }

        private void EnableNetworkThrottling()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile", true);
                if (key != null)
                {
                    key.SetValue("NetworkThrottlingIndex", 10, RegistryValueKind.DWord);
                    key.SetValue("SystemResponsiveness", 20, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Network throttling enable error: {ex.Message}");
            }
        }

        public string GetGameModeStatus()
        {
            return isGameModeActive ? 
                "🎮 GAME MODE: ACTIVE\n⚡ Ultra-Low Latency Enabled\n🚀 Maximum Performance Mode" :
                "🎮 Game Mode: Inactive\n📊 Normal Performance Mode";
        }
    }
}
